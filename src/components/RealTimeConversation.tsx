import React, { useEffect } from 'react';
import { useRealTimeConversation } from '../hooks/useRealTimeConversation';
import type { ConversationMessage } from '../hooks/useRealTimeConversation';

interface RealTimeConversationProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  onLog?: (level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: any) => void;
}

const ConversationStateIndicator: React.FC<{ state: string }> = ({ state }) => {
  const getStateInfo = () => {
    switch (state) {
      case 'idle':
        return { icon: '⭕', text: 'Inactivo', color: '#6c757d' };
      case 'listening':
        return { icon: '🎤', text: 'Escuchando...', color: '#28a745' };
      case 'processing':
        return { icon: '⚙️', text: 'Procesando...', color: '#ffc107' };
      case 'speaking':
        return { icon: '🔊', text: 'IA hablando...', color: '#17a2b8' };
      default:
        return { icon: '❓', text: 'Desconocido', color: '#6c757d' };
    }
  };

  const { icon, text, color } = getStateInfo();

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 12px',
      backgroundColor: '#f8f9fa',
      borderRadius: '20px',
      border: `2px solid ${color}`,
      fontSize: '14px',
      fontWeight: 'bold',
      color: color
    }}>
      <span style={{ fontSize: '16px' }}>{icon}</span>
      <span>{text}</span>
    </div>
  );
};

const MessageBubble: React.FC<{ message: ConversationMessage }> = ({ message }) => {
  const isUser = message.type === 'user';

  return (
    <div style={{
      display: 'flex',
      justifyContent: isUser ? 'flex-end' : 'flex-start',
      marginBottom: '12px'
    }}>
      <div style={{
        maxWidth: '80%',
        padding: '12px 16px',
        borderRadius: '18px',
        backgroundColor: isUser ? '#007bff' : '#e9ecef',
        color: isUser ? 'white' : '#333',
        fontSize: '14px',
        lineHeight: '1.4',
        opacity: message.isInterim ? 0.7 : 1,
        border: message.isInterim ? '2px dashed #ccc' : 'none',
        position: 'relative'
      }}>
        <div style={{ marginBottom: '4px' }}>
          {message.content}
        </div>
        {message.isInterim && (
          <div style={{
            fontSize: '10px',
            opacity: 0.8,
            fontStyle: 'italic'
          }}>
            Escribiendo...
          </div>
        )}
        <div style={{
          fontSize: '10px',
          opacity: 0.7,
          marginTop: '4px',
          textAlign: 'right'
        }}>
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export const RealTimeConversation: React.FC<RealTimeConversationProps> = ({
  generatedCharacter,
  isGameStarted,
  onLog
}) => {
  const {
    isActive,
    conversationState,
    messages,
    currentUserInput,
    isSupported,
    error,
    startConversation,
    stopConversation,
    clearMessages,
    enableSmartMicrophone
  } = useRealTimeConversation(generatedCharacter, onLog);

  // Efecto simplificado para auto-activar cuando el juego inicia
  useEffect(() => {
    console.log('🔍 Checking auto-start conditions:', {
      isGameStarted,
      isActive,
      isSupported
    });

    if (isGameStarted && !isActive && isSupported) {
      onLog?.('info', '🎙️ Juego iniciado - Auto-activando conversación');

      // Delay pequeño para evitar conflictos
      const timer = setTimeout(async () => {
        console.log('⏰ Ejecutando auto-start...');
        try {
          const success = await startConversation();
          if (success) {
            onLog?.('info', '✅ Conversación activada automáticamente');
            enableSmartMicrophone();
            console.log('✅ Auto-start exitoso');
          } else {
            onLog?.('error', '❌ Falló la activación automática');
            console.error('❌ Auto-start falló');
          }
        } catch (error) {
          onLog?.('error', '❌ Error en auto-start', { error });
          console.error('❌ Error:', error);
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isGameStarted, isActive, isSupported]);

  // Log de cambios de estado - simplificado para evitar bucles
  useEffect(() => {
    if (conversationState && onLog) {
      onLog('debug', `Estado de conversación: ${conversationState}`);
    }
  }, [conversationState]); // Solo conversationState, sin onLog

  // Log de errores - simplificado
  useEffect(() => {
    if (error && onLog) {
      onLog('error', `Error en conversación: ${error}`);
    }
  }, [error]); // Solo error, sin onLog

  // Log de mensajes nuevos - simplificado
  useEffect(() => {
    if (messages.length > 0 && onLog) {
      const lastMessage = messages[messages.length - 1];
      if (!lastMessage.isInterim) {
        onLog('info', `Nuevo mensaje ${lastMessage.type}: ${lastMessage.content.substring(0, 50)}...`);
      }
    }
  }, [messages]); // Solo messages, sin onLog

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    onLog?.('warn', 'Reconocimiento de voz no soportado en este navegador');
    return (
      <div style={{
        backgroundColor: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '20px'
      }}>
        <h5 style={{ color: '#856404', marginBottom: '8px' }}>
          ⚠️ Conversación por Voz No Disponible
        </h5>
        <p style={{ color: '#856404', margin: 0, fontSize: '14px' }}>
          Tu navegador no soporta reconocimiento de voz. Puedes usar el chat de texto normal.
        </p>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: '#f8f9fa',
      border: '2px solid #dee2e6',
      borderRadius: '12px',
      padding: '20px',
      marginTop: '20px'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <h5 style={{ color: '#495057', margin: 0 }}>
          🎙️ Conversación en Tiempo Real
          {isGameStarted && !isActive && (
            <span style={{
              fontSize: '12px',
              color: '#28a745',
              marginLeft: '8px',
              fontWeight: 'normal'
            }}>
              (Auto-activándose...)
            </span>
          )}
        </h5>
        <ConversationStateIndicator state={conversationState} />
      </div>

      {error && (
        <div style={{
          backgroundColor: '#f8d7da',
          border: '1px solid #f5c6cb',
          borderRadius: '6px',
          padding: '12px',
          marginBottom: '16px',
          color: '#721c24',
          fontSize: '14px'
        }}>
          ❌ {error}
        </div>
      )}

      <div style={{
        display: 'flex',
        gap: '12px',
        marginBottom: '16px',
        flexWrap: 'wrap'
      }}>
        {!isActive ? (
          <button
            onClick={async () => {
              onLog?.('info', 'Usuario iniciando conversación manualmente');
              const success = await startConversation();
              if (success) {
                enableSmartMicrophone();
                onLog?.('info', 'Conversación iniciada manualmente con éxito');
              }
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            🎤 Iniciar Conversación
          </button>
        ) : (
          <button
            onClick={() => {
              onLog?.('info', 'Usuario deteniendo conversación');
              stopConversation();
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold'
            }}
          >
            🛑 Detener Conversación
          </button>
        )}

        <button
          onClick={() => {
            onLog?.('info', 'Usuario limpiando mensajes del chat');
            clearMessages();
          }}
          style={{
            padding: '10px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          🗑️ Limpiar Chat
        </button>

        <button
          onClick={() => {
            onLog?.('info', 'Activando micrófono inteligente');
            enableSmartMicrophone();
          }}
          style={{
            padding: '10px 20px',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          🧠 Micrófono Inteligente
        </button>
      </div>

      {/* Área de mensajes */}
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        padding: '16px',
        minHeight: '300px',
        maxHeight: '400px',
        overflowY: 'auto',
        marginBottom: '16px'
      }}>
        {messages.length === 0 ? (
          <div style={{
            textAlign: 'center',
            color: '#6c757d',
            fontSize: '14px',
            marginTop: '50px'
          }}>
            <p>🎤 {isGameStarted && !isActive ? 'Activando conversación automáticamente...' : 'Inicia la conversación para comenzar a hablar con la IA'}</p>
            <p style={{ fontSize: '12px', marginTop: '8px' }}>
              El micrófono se activará automáticamente y se desactivará cuando la IA esté hablando
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))
        )}
      </div>

      {/* Indicador de entrada actual */}
      {currentUserInput && (
        <div style={{
          backgroundColor: '#e3f2fd',
          border: '1px solid #bbdefb',
          borderRadius: '6px',
          padding: '8px 12px',
          fontSize: '14px',
          color: '#1976d2'
        }}>
          🎤 Escuchando: "{currentUserInput}"
        </div>
      )}

      {/* Información adicional */}
      <div style={{
        fontSize: '12px',
        color: '#6c757d',
        marginTop: '12px',
        lineHeight: '1.4'
      }}>
        <p style={{ margin: '4px 0' }}>
          💡 <strong>Cómo funciona:</strong>
        </p>
        <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
          <li>La conversación se activa automáticamente al iniciar el juego</li>
          <li>Habla normalmente cuando el estado sea "Escuchando"</li>
          <li>El micrófono se desactiva automáticamente cuando la IA habla</li>
          <li>Se reactiva automáticamente cuando la IA termina</li>
          <li>Esto previene bucles infinitos de audio</li>
          <li>El input de texto arriba es solo un fallback manual</li>
        </ul>
      </div>
    </div>
  );
};
