import axios from "axios";
import type { GenerateInput } from "../types/GenerateInput";
import type { GenerateResponse } from "../types/GenerateResponse";
import type { IAppService } from "./impl/IAppService";
import type { SelectedPresetResponse } from "../types/SelectedPresetResponse";
import { VoicesService } from "./VoicesService";

const BASE_URL = import.meta.env.VITE_LLM_API_URL;
const API_KEY = import.meta.env.VITE_LLM_API_KEY;
const PRESET_GENCHARBOT = import.meta.env.VITE_LLM_PRESETID_GENCHARBOT;
const PRESET_IA_VS_PLAYER = import.meta.env.VITE_LLM_PRESETID_IA_VS_PLAYER;

export class AppService implements IAppService {
  private static instance: AppService;
  private sesid = "";
  private selectedPreset = PRESET_GENCHARBOT;
  private voicesService: VoicesService;
  private audioGeneratedCallback?: (audioUrl: string) => void;
  private audioFinishedCallback?: () => void;

  private constructor() {
    this.voicesService = VoicesService.getInstance();
  }

  static getInstance(): AppService {
    if (!AppService.instance) {
      AppService.instance = new AppService();
      AppService.instance.init();
    }
    return AppService.instance;
  }

  private async init(): Promise<void> {
    const existingSessId = localStorage.getItem("sessid");

    if (existingSessId) {
      this.sesid = existingSessId;
      return;
    }

    try {
      const response = await this.selectPreset();
      this.sesid = response.sesid;
      localStorage.setItem("sessid", this.sesid);
    } catch (error) {
      console.error("Error al inicializar el servicio:", error);
    }
  }

  setSesid(id: string): void {
    this.sesid = id;
  }

  async setPreset(preset: "gencharbot" | "ia_vs_player"): Promise<void> {
    switch (preset) {
      case "gencharbot":
        this.selectedPreset = PRESET_GENCHARBOT;
        break;
      case "ia_vs_player":
        this.selectedPreset = PRESET_IA_VS_PLAYER;
        break;
      default:
        console.warn(`Preset '${preset}' no reconocido.`);
        return;
    }

    return this.updatePreset();
  }

  private async updatePreset(): Promise<void> {
    try {
      const response = await this.selectPreset();
      this.sesid = response.sesid;
    } catch (error) {
      console.error("Error al actualizar el preset:", error);
    }
  }

  private getHeaders(): Record<string, string> {
    return {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      "X-Api-Key": API_KEY || "",
      "X-MG-Ses": this.sesid,
      "X-Frame-Options": "SAMEORIGIN",
    };
  }

  private async handleRequest<T>(request: Promise<any>): Promise<T> {
    try {
      const res = await request;
      if (import.meta.env.MODE === "development") {
        console.log(`🟢 ${res.status} ${res.config.url}`, res.data);
      }
      return res.data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        if (error instanceof Error) {
          console.error(`🔴 RESPONSE ERROR: ${error.message}`);
        } else {
          console.error("🔴 RESPONSE ERROR: Unknown error", error);
        }
      }
      throw new Error("Error al realizar la solicitud");
    }
  }

  async generate(text: string, id?: string): Promise<GenerateResponse> {
    let data: GenerateInput = {
      sesid: this.sesid,
      preset: this.selectedPreset,
      query: text,
      query_args: {},
    };

    if (id) {
      data.query_args.query_id = id;
    }

    return this.handleRequest<GenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );
  }

  async selectPreset(): Promise<SelectedPresetResponse> {
    return this.handleRequest<SelectedPresetResponse>(
      axios.get(`${BASE_URL}preset/${this.selectedPreset}`, {
        headers: this.getHeaders(),
      })
    );
  }

  // Método para configurar el callback de audio
  setAudioCallback(callback: (audioUrl: string) => void): void {
    this.audioGeneratedCallback = callback;
  }

  // Método para configurar el callback de audio finalizado
  setAudioFinishedCallback(callback: () => void): void {
    this.audioFinishedCallback = callback;
  }

  // Método privado para generar audio automáticamente
  private async generateAudioForResponse(responseText: string): Promise<void> {
    try {
      console.log("🔊 Configurando servicio de voces...");

      // Asegurar que el servicio de voces esté configurado
      const voiceConfigured = await this.voicesService.configVoice("neutral");
      if (!voiceConfigured) {
        console.warn("⚠️ No se pudo configurar el servicio de voces");
        return;
      }

      console.log("🔊 Generando audio para:", responseText);
      const audioBlob = await this.voicesService.getAudio(responseText);
      const audioUrl = URL.createObjectURL(audioBlob);

      // Llamar al callback si está configurado
      if (this.audioGeneratedCallback) {
        this.audioGeneratedCallback(audioUrl);
      }

      // Estimar duración del audio y llamar callback cuando termine
      if (this.audioFinishedCallback) {
        const estimatedDuration = Math.max(3000, responseText.length * 100); // ~100ms por carácter
        setTimeout(() => {
          if (this.audioFinishedCallback) {
            this.audioFinishedCallback();
          }
        }, estimatedDuration);
      }

      console.log("✅ Audio generado exitosamente");
    } catch (error) {
      console.error("❌ Error generando audio:", error);
    }
  }

  // Método privado para procesar respuesta del preset IA vs Player
  private async processIaVsPlayerResponse(
    response: GenerateResponse
  ): Promise<GenerateResponse> {
    try {
      // Buscar el campo que contiene la respuesta
      let responseText =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content;

      // Siempre generar audio para cualquier respuesta de texto
      if (responseText && typeof responseText === "string") {
        let audioText = "";
        let displayText = "";

        // Si la respuesta es un string que parece JSON, parsearlo
        if (responseText.trim().startsWith("{")) {
          try {
            const parsedResponse = JSON.parse(responseText);
            console.log("🎮 Respuesta parseada del juego:", parsedResponse);

            if (parsedResponse.respuesta) {
              audioText = parsedResponse.respuesta;

              // Formatear la respuesta para mostrar
              displayText = `🤖 IA: ${parsedResponse.respuesta}`;

              if (parsedResponse.pista) {
                displayText += `\n💡 Pista: ${parsedResponse.pista}`;
              }

              if (parsedResponse.cuenta_regresiva !== undefined) {
                displayText += `\n⏰ Preguntas restantes: ${parsedResponse.cuenta_regresiva}`;
              }

              if (parsedResponse.acertado) {
                displayText += `\n🎉 ¡La IA ha acertado!`;
              }

              if (parsedResponse.juego_finalizado) {
                displayText += `\n🏁 Juego finalizado`;
              }
            }
          } catch (parseError) {
            console.error("Error parseando JSON:", parseError);
            // Si no se puede parsear, usar el texto original
            audioText = responseText;
            displayText = responseText;
          }
        } else {
          // Para respuestas de texto simple (como el saludo inicial)
          audioText = responseText;
          displayText = responseText;
        }

        // SIEMPRE generar audio si hay texto
        if (audioText) {
          console.log("🔊 Generando audio para:", audioText);
          await this.generateAudioForResponse(audioText);
        }

        // Devolver la respuesta con el texto formateado
        return {
          ...response,
          response: displayText || responseText,
        };
      }

      return response;
    } catch (error) {
      console.error("Error procesando respuesta:", error);
      return response;
    }
  }
  // Métodos específicos para cada preset con diferentes comportamientos
  async generateWithGenCharBot(
    text: string,
    id?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("gencharbot");
    return this.generate(text, id);
  }

  async generateWithIaVsPlayer(
    text: string,
    id?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("ia_vs_player");
    const response = await this.generate(text, id);

    // Procesar la respuesta y generar audio automáticamente
    return this.processIaVsPlayerResponse(response);
  }

  getCurrentPreset(): string {
    return this.selectedPreset;
  }
}
