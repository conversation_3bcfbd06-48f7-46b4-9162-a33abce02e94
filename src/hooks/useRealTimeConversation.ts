import { useState, useEffect, useCallback, useRef } from 'react';
import { SpeechRecognitionService } from '../services/SpeechRecognitionService';
import { AppService } from '../services/AppService';
import type { ConversationState, SpeechRecognitionResult } from '../services/impl/ISpeechRecognitionService';

export interface ConversationMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isInterim?: boolean;
}

export interface UseRealTimeConversationReturn {
  // Estado
  isActive: boolean;
  conversationState: ConversationState;
  messages: ConversationMessage[];
  currentUserInput: string;
  isSupported: boolean;
  error: string | null;

  // Controles
  startConversation: () => Promise<boolean>;
  stopConversation: () => void;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;

  // Configuración
  enableSmartMicrophone: () => void;
  disableSmartMicrophone: () => void;
}

export const useRealTimeConversation = (
  generatedCharacter?: string,
  onLog?: (level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: any) => void
): UseRealTimeConversationReturn => {
  const [isActive, setIsActive] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>('idle');
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [currentUserInput, setCurrentUserInput] = useState('');
  const [error, setError] = useState<string | null>(null);

  const speechService = useRef(SpeechRecognitionService.getInstance());
  const appService = useRef(AppService.getInstance());
  const messageIdCounter = useRef(0);

  const isSupported = speechService.current.isSupported();

  // Logging helper - estabilizado con useCallback
  const log = useCallback((level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: any) => {
    if (onLog) {
      onLog(level, `[RealTimeConversation] ${message}`, data);
    }
  }, []); // SIN dependencias para evitar bucles

  // Generar ID único para mensajes
  const generateMessageId = useCallback(() => {
    return `msg_${Date.now()}_${++messageIdCounter.current}`;
  }, []);

  // Agregar mensaje a la conversación
  const addMessage = useCallback((type: 'user' | 'ai', content: string, isInterim = false) => {
    const newMessage: ConversationMessage = {
      id: generateMessageId(),
      type,
      content,
      timestamp: new Date(),
      isInterim
    };

    setMessages(prev => {
      // Si es un mensaje interim del usuario, reemplazar el último mensaje interim
      if (isInterim && type === 'user') {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && lastMessage.type === 'user' && lastMessage.isInterim) {
          return [...prev.slice(0, -1), newMessage];
        }
      }
      return [...prev, newMessage];
    });

    if (!isInterim) {
      log('debug', `Mensaje añadido: ${type} - ${content.substring(0, 50)}...`);
    }

    return newMessage.id;
  }, [generateMessageId, log]);

  // Finalizar mensaje interim
  const finalizeMessage = useCallback((content: string) => {
    setMessages(prev => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage && lastMessage.type === 'user' && lastMessage.isInterim) {
        log('debug', `Mensaje finalizado: ${content}`);
        return [
          ...prev.slice(0, -1),
          { ...lastMessage, content, isInterim: false }
        ];
      }
      return prev;
    });
  }, [log]);

  // Enviar mensaje a la IA
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) {
      log('warn', 'Intento de enviar mensaje vacío');
      return;
    }

    try {
      log('info', `Enviando mensaje: ${message}`);
      setConversationState('processing');
      speechService.current.setConversationState('processing');

      // Incluir información del personaje en el contexto si está disponible
      const queryWithContext = generatedCharacter
        ? `Personaje a adivinar: ${generatedCharacter}\n\nRespuesta del jugador: ${message}`
        : message;

      log('debug', 'Query con contexto preparado', { queryWithContext });

      const response = await appService.current.generateWithIaVsPlayer(queryWithContext);
      const responseText = response.response || response.output || response.result || response.text || response.content || 'Respuesta no encontrada';

      log('info', 'Respuesta recibida de la IA', {
        responseLength: responseText.length,
        responsePreview: responseText.substring(0, 100) + '...'
      });

      // Agregar respuesta de la IA
      addMessage('ai', responseText);

      // Cambiar estado a "hablando" - el audio se maneja automáticamente por AppService
      log('debug', 'Cambiando estado a "speaking"');
      setConversationState('speaking');
      speechService.current.setConversationState('speaking');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      log('error', 'Error enviando mensaje a la IA', { error: errorMessage });
      setError('Error al procesar el mensaje');
      setConversationState('idle');
      speechService.current.setConversationState('idle');
    }
  }, [generatedCharacter, addMessage, log]);

  // Manejar resultados del reconocimiento de voz
  const handleSpeechResult = useCallback((result: SpeechRecognitionResult) => {
    const { transcript, confidence, isFinal } = result;

    log('debug', `Resultado de reconocimiento: ${transcript}`, {
      confidence,
      isFinal,
      transcriptLength: transcript.length
    });

    if (transcript.trim()) {
      if (isFinal) {
        // Finalizar el mensaje y enviarlo
        finalizeMessage(transcript);
        setCurrentUserInput('');
        sendMessage(transcript);
      } else {
        // Mostrar resultado interim
        addMessage('user', transcript, true);
        setCurrentUserInput(transcript);
      }
    }
  }, [addMessage, finalizeMessage, sendMessage, log]);

  // Manejar cambios de estado
  const handleStateChange = useCallback((change: any) => {
    log('debug', `Cambio de estado de conversación: ${change.state}`);
    setConversationState(change.state);
  }, [log]);

  // Manejar errores
  const handleError = useCallback((errorMessage: string) => {
    log('error', `Error en reconocimiento de voz: ${errorMessage}`);
    setError(errorMessage);
    setConversationState('idle');
  }, [log]);

  // Manejar cuando el audio termine
  const handleAudioFinished = useCallback(() => {
    log('debug', 'Audio terminado, volviendo a estado idle');
    if (isActive) {
      setConversationState('idle');
      speechService.current.setConversationState('idle');
    }
  }, [isActive, log]);

  // Inicializar callbacks del servicio de voz
  useEffect(() => {
    log('debug', 'Inicializando callbacks de servicios');

    speechService.current.onResult(handleSpeechResult);
    speechService.current.onStateChange(handleStateChange);
    speechService.current.onError(handleError);

    // Configurar callback para cuando termine el audio
    appService.current.setAudioFinishedCallback(handleAudioFinished);
  }, [handleSpeechResult, handleStateChange, handleError, handleAudioFinished, log]);

  // Iniciar conversación
  const startConversation = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      const errorMsg = 'El reconocimiento de voz no está soportado en este navegador';
      log('error', errorMsg);
      setError(errorMsg);
      return false;
    }

    log('info', 'Iniciando conversación en tiempo real');
    setError(null);
    setIsActive(true);

    const success = await speechService.current.startListening();
    if (!success) {
      setIsActive(false);
      const errorMsg = 'No se pudo iniciar el reconocimiento de voz';
      log('error', errorMsg);
      setError(errorMsg);
      return false;
    }

    log('info', 'Conversación en tiempo real iniciada exitosamente');
    return true;
  }, [isSupported, log]);

  // Detener conversación
  const stopConversation = useCallback(() => {
    log('info', 'Deteniendo conversación en tiempo real');
    speechService.current.stopListening();
    setIsActive(false);
    setConversationState('idle');
    setCurrentUserInput('');
  }, [log]);

  // Limpiar mensajes
  const clearMessages = useCallback(() => {
    log('info', 'Limpiando mensajes de conversación');
    setMessages([]);
    setCurrentUserInput('');
    setError(null);
  }, [log]);

  // Control inteligente del micrófono
  const enableSmartMicrophone = useCallback(() => {
    log('info', 'Activando control inteligente de micrófono');
    speechService.current.enableSmartMicrophoneControl();
  }, [log]);

  const disableSmartMicrophone = useCallback(() => {
    log('info', 'Desactivando control inteligente de micrófono');
    speechService.current.disableSmartMicrophoneControl();
  }, [log]);

  // Log inicial de soporte
  useEffect(() => {
    log('info', `Hook inicializado`, {
      isSupported,
      hasGeneratedCharacter: !!generatedCharacter,
      characterPreview: generatedCharacter?.substring(0, 30)
    });
  }, [isSupported, generatedCharacter, log]);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      if (isActive) {
        log('info', 'Limpieza al desmontar - deteniendo conversación');
        stopConversation();
      }
    };
  }, [isActive, stopConversation, log]);

  return {
    // Estado
    isActive,
    conversationState,
    messages,
    currentUserInput,
    isSupported,
    error,

    // Controles
    startConversation,
    stopConversation,
    sendMessage,
    clearMessages,

    // Configuración
    enableSmartMicrophone,
    disableSmartMicrophone,
  };
};
