// Utilidades para manejo de audio

export const playAudioWithFallback = async (audioUrl: string): Promise<void> => {
  try {
    // Crear un elemento de audio temporal
    const audio = new Audio(audioUrl);
    
    // Configurar el audio
    audio.preload = 'auto';
    audio.volume = 0.8;
    
    // Intentar reproducir
    await audio.play();
    console.log('✅ Audio reproducido exitosamente');
    
  } catch (error) {
    console.warn('⚠️ No se pudo reproducir automáticamente:', error);
    
    // Fallback: mostrar notificación al usuario
    if (error instanceof Error && error.name === 'NotAllowedError') {
      console.log('🔇 Autoplay bloqueado por el navegador');
    }
  }
};

export const createAudioElement = (audioUrl: string, autoplay: boolean = true): HTMLAudioElement => {
  const audio = new Audio(audioUrl);
  audio.controls = true;
  audio.preload = 'auto';
  audio.volume = 0.8;
  
  if (autoplay) {
    // Intentar reproducir cuando esté listo
    audio.addEventListener('canplaythrough', () => {
      audio.play().catch(error => {
        console.warn('⚠️ Autoplay bloqueado:', error);
      });
    });
  }
  
  return audio;
};
