import { useState, useEffect } from "react";
import "./App.css";
import { AppService } from "./services/AppService";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { RealTimeConversation } from "./components/RealTimeConversation";

// Componente de Logs
interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: any;
}

const LogsPanel: React.FC<{ logs: LogEntry[], onClear: () => void }> = ({ logs, onClear }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return '#dc3545';
      case 'warn': return '#ffc107';
      case 'info': return '#17a2b8';
      case 'debug': return '#6c757d';
      default: return '#333';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'info': return 'ℹ️';
      case 'debug': return '🔍';
      default: return '📝';
    }
  };

  return (
    <div style={{
      marginTop: "20px",
      border: "1px solid #ccc",
      borderRadius: "8px",
      backgroundColor: "#f8f9fa"
    }}>
      <div
        style={{
          padding: "10px 15px",
          backgroundColor: "#e9ecef",
          borderRadius: "8px 8px 0 0",
          cursor: "pointer",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h4 style={{ margin: 0, color: "#495057" }}>
          📋 Sistema de Logs ({logs.length})
        </h4>
        <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClear();
            }}
            style={{
              padding: "4px 8px",
              fontSize: "12px",
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer"
            }}
          >
            Limpiar
          </button>
          <span>{isExpanded ? '🔽' : '▶️'}</span>
        </div>
      </div>

      {isExpanded && (
        <div style={{
          maxHeight: "300px",
          overflowY: "auto",
          padding: "10px"
        }}>
          {logs.length === 0 ? (
            <p style={{ textAlign: "center", color: "#6c757d", margin: "20px 0" }}>
              No hay logs disponibles
            </p>
          ) : (
            logs.slice().reverse().map((log) => (
              <div
                key={log.id}
                style={{
                  padding: "8px 12px",
                  marginBottom: "8px",
                  backgroundColor: "white",
                  border: `1px solid ${getLevelColor(log.level)}`,
                  borderRadius: "4px",
                  borderLeft: `4px solid ${getLevelColor(log.level)}`
                }}
              >
                <div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "4px" }}>
                  <span>{getLevelIcon(log.level)}</span>
                  <span style={{
                    color: getLevelColor(log.level),
                    fontWeight: "bold",
                    fontSize: "12px"
                  }}>
                    {log.level.toUpperCase()}
                  </span>
                  <span style={{ fontSize: "11px", color: "#6c757d" }}>
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <div style={{ fontSize: "13px", color: "#333" }}>
                  {log.message}
                </div>
                {log.data && (
                  <details style={{ marginTop: "4px" }}>
                    <summary style={{ fontSize: "11px", color: "#6c757d", cursor: "pointer" }}>
                      Ver datos
                    </summary>
                    <pre style={{
                      fontSize: "10px",
                      backgroundColor: "#f8f9fa",
                      padding: "4px",
                      borderRadius: "2px",
                      overflow: "auto",
                      maxHeight: "100px"
                    }}>
                      {JSON.stringify(log.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

function App() {
  const [audioUrl, setAudioUrl] = useState<string>("");

  // Estados para el servicio de IA
  const [aiQuery, setAiQuery] = useState<string>("");
  const [aiResponse, setAiResponse] = useState<string>("");
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);

  // Sistema de logs
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [logIdCounter, setLogIdCounter] = useState(0);

  // Estado para controlar la conversación en tiempo real
  const [shouldStartRealTimeConversation, setShouldStartRealTimeConversation] = useState(false);

  // Efecto separado para resetear el shouldAutoStart
  useEffect(() => {
    if (shouldStartRealTimeConversation && gameStarted) {
      // Resetear después de un momento para evitar bucles
      const timer = setTimeout(() => {
        setShouldStartRealTimeConversation(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [shouldStartRealTimeConversation, gameStarted]);

  const appService = AppService.getInstance();

  // Función para añadir logs
  const addLog = (level: LogEntry['level'], message: string, data?: any) => {
    const newLog: LogEntry = {
      id: `log_${Date.now()}_${logIdCounter}`,
      timestamp: new Date(),
      level,
      message,
      data
    };

    setLogs(prev => [...prev.slice(-99), newLog]); // Mantener solo los últimos 100 logs
    setLogIdCounter(prev => prev + 1);

    // También loggear en consola para desarrollo
    const consoleMessage = `[${level.toUpperCase()}] ${message}`;
    switch (level) {
      case 'error':
        console.error(consoleMessage, data);
        break;
      case 'warn':
        console.warn(consoleMessage, data);
        break;
      case 'info':
        console.log(consoleMessage, data);
        break;
      case 'debug':
        console.debug(consoleMessage, data);
        break;
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setLogIdCounter(0);
    addLog('info', 'Logs limpiados');
  };

  // Configurar el callback de audio en el servicio
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      setAudioUrl(audioUrl);
      addLog('info', 'Audio generado', { url: audioUrl.substring(0, 50) + '...' });
      // Intentar reproducir automáticamente
      setTimeout(() => {
        playAudioWithFallback(audioUrl);
      }, 100);
    });
  }, [appService]);

  // Funciones para el servicio de IA
  const handleGenerateCharacter = async () => {
    setAiLoading(true);
    setAiResponse("");
    addLog('info', 'Iniciando generación de personaje');

    try {
      addLog('debug', 'Enviando query al servicio GenCharBot', {
        query: GAME_MESSAGES.GENERATE_CHARACTER
      });

      const response = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      addLog('info', 'Respuesta recibida del servicio', {
        responseKeys: Object.keys(response),
        hasResponse: !!response.response
      });

      // Buscar el campo que contiene la respuesta del personaje
      const characterResponse =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content;

      if (characterResponse) {
        setAiResponse(characterResponse);
        setGeneratedCharacter(characterResponse);
        addLog('info', 'Personaje generado exitosamente', {
          character: characterResponse
        });
      } else {
        addLog('error', 'No se encontró el campo de respuesta en la API', {
          responseStructure: response,
          availableFields: Object.keys(response)
        });

        const responseText = JSON.stringify(response, null, 2);
        setAiResponse(`Debug - Respuesta completa: ${responseText}`);

        // Intentar extraer cualquier texto de la respuesta
        const fallbackText = response.input || response.query || "Personaje de prueba";
        addLog('warn', 'Usando texto de fallback', { fallbackText });
        setGeneratedCharacter(fallbackText);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', 'Error generando personaje', { error: errorMessage });
      setAiResponse("Error al generar el personaje. Por favor, inténtalo de nuevo.");
    } finally {
      setAiLoading(false);
    }
  };

  const handleStartGame = async () => {
    addLog('info', 'Iniciando juego', { character: generatedCharacter });

    if (!generatedCharacter.trim()) {
      addLog('warn', 'Intento de iniciar juego sin personaje generado');
      alert("Primero debes generar un personaje");
      return;
    }

    setAiLoading(true);
    setAiResponse("");
    setGameStarted(true);

    try {
      addLog('debug', 'Enviando query inicial del juego', {
        query: GAME_MESSAGES.INITIAL_GAME_QUERY
      });

      // 1. Obtener respuesta del servicio de IA
      const response = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY
      );

      addLog('info', 'Respuesta inicial del juego recibida', {
        responseKeys: Object.keys(response)
      });

      // 2. El servicio ahora maneja automáticamente el audio y el formato de respuesta
      const responseText =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        "Respuesta no encontrada";

      addLog('debug', 'Respuesta procesada', { responseText });

      // 3. Actualizar estado
      setAiResponse(responseText);

      // 4. Activar la conversación en tiempo real automáticamente
      setShouldStartRealTimeConversation(true);
      addLog('info', 'Juego iniciado - Activando conversación en tiempo real', {
        shouldStartRealTimeConversation: true,
        gameStarted: true
      });
      console.log('🎮 Estado actualizado para auto-start:', {
        shouldStartRealTimeConversation: true,
        gameStarted: true
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', 'Error iniciando el juego', { error: errorMessage });
      setAiResponse("Error al iniciar el juego. Por favor, inténtalo de nuevo.");
    } finally {
      setAiLoading(false);
    }
  };

  const handleContinueGame = async () => {
    if (!aiQuery.trim()) {
      addLog('warn', 'Intento de continuar juego con query vacío');
      return;
    }

    setAiLoading(true);
    addLog('info', 'Continuando juego con respuesta manual', { query: aiQuery });

    try {
      // Incluir información del personaje en el contexto
      const queryWithContext = `Personaje a adivinar: ${generatedCharacter}\n\nRespuesta del jugador: ${aiQuery}`;

      addLog('debug', 'Enviando query con contexto', { queryWithContext });

      const response = await appService.generateWithIaVsPlayer(queryWithContext);

      // El servicio ahora maneja automáticamente el audio y el formato de respuesta
      const responseText =
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        "Respuesta no encontrada";

      setAiResponse(responseText);
      setAiQuery(""); // Limpiar el campo después de enviar

      addLog('info', 'Respuesta del juego procesada', { responseText });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      addLog('error', 'Error continuando el juego', { error: errorMessage });
      setAiResponse("Error al continuar el juego. Por favor, inténtalo de nuevo.");
    } finally {
      setAiLoading(false);
    }
  };

  // Variables de entorno
  const config = {
    // Configuración de la API de IA
    llmApiUrl: import.meta.env.VITE_LLM_API_URL,
    llmApiKey: import.meta.env.VITE_LLM_API_KEY,
    llmPresetIdGenCharBot: import.meta.env.VITE_LLM_PRESETID_GENCHARBOT,
    llmPresetIdIaVsPlayer: import.meta.env.VITE_LLM_PRESETID_IA_VS_PLAYER,

    // Configuración de Speech API
    speechApiUrl: import.meta.env.VITE_SPEECH_API_URL,
    speechApiKey: import.meta.env.VITE_SPEECH_API_KEY,

    // Configuración de Movistar Plus API
    movistarApiUrl: import.meta.env.VITE_MOVISTAR_API_URL,

    // Configuración Perplexity
    perplexityId: import.meta.env.VITE_PERPLEXITY_ID,
  };

  // Log inicial
  useEffect(() => {
    addLog('info', 'Aplicación inicializada', {
      configStatus: {
        llmApiUrl: !!config.llmApiUrl,
        llmApiKey: !!config.llmApiKey,
        speechApiUrl: !!config.speechApiUrl,
        speechApiKey: !!config.speechApiKey
      }
    });
  }, []);

  return (
    <>
      <h1>Genigma WB - Configuración</h1>
      <div className="card">
        <div
          style={{
            marginTop: "30px",
            padding: "20px",
            border: "1px solid #ccc",
            borderRadius: "8px",
          }}
        >
          <h3>Juego de Adivinanza de Personajes</h3>

          {/* Paso 1: Generar Personaje */}
          <div
            style={{
              marginBottom: "20px",
              padding: "15px",
              backgroundColor: "#f0f8ff",
              borderRadius: "8px",
            }}
          >
            <h4 style={{ color: "#007bff" }}>Paso 1: Generar Personaje</h4>
            <p style={{ color: "#333" }}>
              Primero, genera un personaje que la IA tendrá que adivinar:
            </p>

            {/* Debug info */}
            <div
              style={{ fontSize: "12px", color: "#666", marginBottom: "10px" }}
            >
              <strong>Debug:</strong> generatedCharacter = "{generatedCharacter}
              " | aiLoading = {aiLoading.toString()} | aiResponse length ={" "}
              {aiResponse.length} | gameStarted = {gameStarted.toString()}
              <br />
              <strong>Config:</strong> URL = {config.llmApiUrl || "NO_SET"} |
              Key = {config.llmApiKey ? "SET" : "NO_SET"} | Preset ={" "}
              {config.llmPresetIdGenCharBot || "NO_SET"}
              <br />
              <strong>Condición botón:</strong> generatedCharacter.length ={" "}
              {generatedCharacter.length} | !gameStarted ={" "}
              {(!gameStarted).toString()} | Mostrar botón ={" "}
              {(generatedCharacter && !gameStarted).toString()}
            </div>
            <button
              onClick={handleGenerateCharacter}
              disabled={aiLoading}
              style={{
                padding: "10px 20px",
                backgroundColor: aiLoading ? "#ccc" : "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: aiLoading ? "not-allowed" : "pointer",
                marginRight: "10px",
              }}
            >
              {aiLoading ? "Generando..." : "Generar Personaje"}
            </button>

            {/* Botón de debug temporal */}
            <button
              onClick={() => {
                addLog('debug', 'Forzando personaje de prueba');
                setGeneratedCharacter("Personaje de prueba para debug");
                setGameStarted(false);
              }}
              style={{
                padding: "10px 20px",
                backgroundColor: "#ffc107",
                color: "black",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              🔧 Debug: Forzar Personaje
            </button>

            {generatedCharacter ? (
              <div
                style={{
                  marginTop: "10px",
                  padding: "10px",
                  color: "#155724",
                  backgroundColor: "#d4edda",
                  border: "1px solid #c3e6cb",
                  borderRadius: "4px",
                }}
              >
                <strong>Personaje generado:</strong> {generatedCharacter}
              </div>
            ) : (
              aiResponse &&
              !aiLoading && (
                <div
                  style={{
                    marginTop: "10px",
                    padding: "10px",
                    color: "#721c24",
                    backgroundColor: "#f8d7da",
                    border: "1px solid #f5c6cb",
                    borderRadius: "4px",
                  }}
                >
                  <strong>Estado:</strong> {aiResponse}
                </div>
              )
            )}
          </div>

          {/* Paso 2: Iniciar Juego */}
          {generatedCharacter && !gameStarted && (
            <div
              style={{
                marginBottom: "20px",
                padding: "15px",
                color: "black",
                backgroundColor: "#fff3cd",
                borderRadius: "8px",
              }}
            >
              <h4>Paso 2: Iniciar el Juego</h4>
              <p>
                Ahora inicia el juego donde la IA intentará adivinar tu
                personaje. <strong>Se activará automáticamente la conversación por voz.</strong>
              </p>
              <button
                onClick={handleStartGame}
                disabled={aiLoading}
                style={{
                  padding: "10px 20px",
                  backgroundColor: aiLoading ? "#ccc" : "#007bff",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: aiLoading ? "not-allowed" : "pointer",
                }}
              >
                {aiLoading ? "Iniciando..." : "🎙️ Iniciar Juego + Conversación"}
              </button>
            </div>
          )}

          {/* Paso 3: Continuar Juego (fallback manual) */}
          {gameStarted && (
            <div
              style={{
                marginBottom: "20px",
                padding: "15px",
                backgroundColor: "#e2e3e5",
                borderRadius: "8px",
              }}
            >
              <h4>Paso 3: Responder (Fallback Manual)</h4>
              <p>
                <strong>La conversación por voz está activa abajo.</strong> Este campo es solo un fallback
                si necesitas escribir algo manualmente:
              </p>

              <div style={{ marginBottom: "15px" }}>
                <textarea
                  value={aiQuery}
                  onChange={(e) => setAiQuery(e.target.value)}
                  style={{ width: "100%", height: "60px", padding: "8px" }}
                  placeholder="Escribe tu respuesta aquí solo si necesitas el fallback manual..."
                />
              </div>

              <button
                onClick={handleContinueGame}
                disabled={aiLoading || !aiQuery.trim()}
                style={{
                  padding: "10px 20px",
                  backgroundColor: aiLoading ? "#ccc" : "#17a2b8",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: aiLoading ? "not-allowed" : "pointer",
                }}
              >
                {aiLoading ? "Enviando..." : "Enviar Respuesta Manual"}
              </button>
            </div>
          )}

          {/* Respuesta de la IA */}
          {aiResponse && (
            <div
              style={{
                marginTop: "15px",
                padding: "15px",
                color: "#0c5460",
                backgroundColor: "#f8f9fa",
                border: "1px solid #dee2e6",
                borderRadius: "4px",
              }}
            >
              <h4>Respuesta de la IA:</h4>
              <p style={{ whiteSpace: "pre-wrap", margin: "10px 0 0 0" }}>
                {aiResponse}
              </p>

              {/* Reproductor de audio automático */}
              {audioUrl && gameStarted && (
                <div style={{ marginTop: "15px" }}>
                  <h5 style={{ color: "#0c5460" }}>
                    🔊 Audio de la respuesta:
                  </h5>
                  <audio controls src={audioUrl} style={{ width: "100%" }}>
                    Tu navegador no soporta el elemento de audio.
                  </audio>
                  <div style={{ marginTop: "10px" }}>
                    <button
                      onClick={() => {
                        const audioElement = document.querySelector(
                          "audio"
                        ) as HTMLAudioElement;
                        if (audioElement) {
                          audioElement.currentTime = 0;
                          audioElement.play().catch((error) => {
                            addLog('error', 'Error reproduciendo audio', { error });
                          });
                        }
                      }}
                      style={{
                        padding: "5px 10px",
                        backgroundColor: "#17a2b8",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                        fontSize: "12px",
                      }}
                    >
                      🔊 Reproducir Audio
                    </button>
                    <p
                      style={{
                        fontSize: "12px",
                        color: "#666",
                        marginTop: "5px",
                      }}
                    >
                      💡 Si el audio no se reproduce automáticamente, usa el
                      botón de arriba
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Componente de Conversación en Tiempo Real */}
          <RealTimeConversation
            generatedCharacter={generatedCharacter}
            isGameStarted={gameStarted}
            onLog={addLog}
          />

          {/* Botón para reiniciar */}
          {(generatedCharacter || gameStarted) && (
            <div style={{ marginTop: "20px", textAlign: "center" }}>
              <button
                onClick={() => {
                  addLog('info', 'Reiniciando juego');
                  setGeneratedCharacter("");
                  setGameStarted(false);
                  setAiResponse("");
                  setAiQuery("");
                  setShouldStartRealTimeConversation(false);
                }}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#6c757d",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                🔄 Reiniciar Juego
              </button>
            </div>
          )}
        </div>

        {/* Panel de Logs */}
        <LogsPanel logs={logs} onClear={clearLogs} />
      </div>
    </>
  );
}

export default App;
